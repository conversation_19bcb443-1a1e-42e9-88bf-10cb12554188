package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.*;
import com.talkweb.ai.indexer.util.JsonUtils;
import com.talkweb.ai.indexer.util.PluginUtils;
import com.talkweb.ai.indexer.util.YamlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.file.*;
import java.nio.file.StandardWatchEventKinds;
import java.nio.file.WatchService;
import java.nio.file.WatchKey;
import java.nio.file.WatchEvent;
import java.io.InputStream;
import java.io.IOException;
import java.util.concurrent.TimeUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 默认插件管理器实现
 */
public class DefaultPluginManager implements PluginManager {
    private static final Logger log = LoggerFactory.getLogger(DefaultPluginManager.class);
    private static final String CONFIG_DIR = "config";
    private static final String PLUGIN_CONFIG_PREFIX = "plugin-";
    private static final String PLUGIN_GLOBAL_CONFIG = "plugin-global";

    private final PluginRegistry pluginRegistry;
    private volatile boolean isRunning;
    private volatile boolean hotReloadEnabled = false;
    private WatchService watchService;
    private Thread watchThread;
    private final PluginConfig config;
    private final Set<String> blacklist;
    private final Set<String> whitelist;
    private boolean useWhitelist;
    private final List<Consumer<PluginEvent>> eventListeners = new CopyOnWriteArrayList<>();
    private final Map<String, PluginState> pluginStates = new ConcurrentHashMap<>();
    private final Map<String, ClassLoader> pluginClassLoaders = new ConcurrentHashMap<>();
    private final Map<String, Properties> pluginConfigs = new ConcurrentHashMap<>();
    private final Path pluginsDir;
    private final Path tempDir;
    private final Path configDir;

    public DefaultPluginManager(Path pluginsDir, Path tempDir) {
        this(pluginsDir, tempDir, pluginsDir.getParent().resolve(CONFIG_DIR));
    }

    public DefaultPluginManager(Path pluginsDir, Path tempDir, Path configDir) {
        this(pluginsDir, tempDir, configDir, new DefaultPluginRegistry(), new PluginConfig());
    }

    public DefaultPluginManager(Path pluginsDir, Path tempDir, Path configDir, 
                              PluginRegistry pluginRegistry, PluginConfig config) {
        this.pluginsDir = pluginsDir.toAbsolutePath();
        this.tempDir = tempDir.resolve("plugins").toAbsolutePath();
        this.configDir = configDir.toAbsolutePath();
        this.pluginRegistry = pluginRegistry;
        this.config = config;
        this.blacklist = new HashSet<>(config.getBlacklist());
        this.whitelist = new HashSet<>(config.getWhitelist());
        this.useWhitelist = "WHITELIST".equalsIgnoreCase(config.getMode());

        // 确保插件目录存在
        if (!pluginsDir.toFile().exists()) {
            pluginsDir.toFile().mkdirs();
        }

        // 确保临时目录存在
        if (!this.tempDir.toFile().exists()) {
            this.tempDir.toFile().mkdirs();
        }

        // 确保配置目录存在
        if (!this.configDir.toFile().exists()) {
            this.configDir.toFile().mkdirs();
        }
    }

    @Override
    public void enableHotReload(Path directory) throws PluginException {
        if (hotReloadEnabled) {
            throw new PluginException("Hot reload already enabled");
        }
        
        try {
            this.pollInterval = config.getPollInterval();
            this.watchService = FileSystems.getDefault().newWatchService();
            
            directory.register(watchService, 
                StandardWatchEventKinds.ENTRY_CREATE,
                StandardWatchEventKinds.ENTRY_DELETE,
                StandardWatchEventKinds.ENTRY_MODIFY);
            
            this.watchThread = new Thread(this::watchPluginsDir, "PluginHotReload-Watcher");
            watchThread.setDaemon(true);
            watchThread.start();
            
            hotReloadEnabled = true;
            log.info("Enabled plugin hot reload for directory: {}", directory);
        } catch (IOException e) {
            throw new PluginException("Failed to enable hot reload", e);
        }
    }
            this.tempDir.toFile().mkdirs();
        }

        // 确保配置目录存在
        if (!this.configDir.toFile().exists()) {
            this.configDir.toFile().mkdirs();
        }

        // 加载全局插件配置
        loadGlobalPluginConfig();
    }

    /**
     * 启动插件管理器
     * @throws PluginException 如果启动失败
     */
    @Override
    public void start() throws PluginException {
        if (isRunning) {
            return;
        }

        try {
            loadPlugins();
            initPlugins();
            startPlugins();
            isRunning = true;
            log.info("Plugin manager started successfully");
        } catch (Exception e) {
            log.error("Failed to start plugin manager", e);
            throw new PluginException("Failed to start plugin manager", e);
        }
    }

    /**
     * 关闭插件管理器
     */
    public void shutdown() {
        if (!isRunning) {
            return;
        }

        try {
            stopPlugins();
            destroyPlugins();
        } catch (Exception e) {
            log.error("Error during plugin manager shutdown", e);
        } finally {
            isRunning = false;
            log.info("Plugin manager shut down");
        }
    }

    @Override
    public void loadPlugins() throws PluginException {
        log.info("Loading plugins from: {}", pluginsDir);

        File[] pluginFiles = pluginsDir.toFile().listFiles((dir, name) ->
            name.endsWith(".jar") || name.endsWith(".zip")
        );

        if (pluginFiles == null || pluginFiles.length == 0) {
            log.info("No plugins found in {}", pluginsDir);
            return;
        }

        List<String> failedPlugins = new ArrayList<>();
        for (File pluginFile : pluginFiles) {
            try {
                loadPlugin(pluginFile);
            } catch (Exception e) {
                String errorMsg = "Failed to load plugin: " + pluginFile.getName();
                log.error(errorMsg, e);
                failedPlugins.add(pluginFile.getName());
            }
        }

        if (!failedPlugins.isEmpty()) {
            throw new PluginException("Failed to load plugins: " + String.join(", ", failedPlugins));
        }

        log.info("Successfully loaded {} plugins", pluginRegistry.size());
    }

    @Override
    public void initPlugins() throws PluginException {
        log.info("Initializing plugins...");
        List<String> failedPlugins = new ArrayList<>();
        for (Plugin plugin : pluginRegistry.getAllPlugins()) {
            try {
                initPlugin(plugin);
            } catch (Exception e) {
                String pluginId = plugin.getMetadata() != null ? plugin.getMetadata().getId() : "unknown";
                log.error("Failed to initialize plugin: " + pluginId, e);
                failedPlugins.add(pluginId);
            }
        }
        if (!failedPlugins.isEmpty()) {
            throw new PluginException("Failed to initialize plugins: " + String.join(", ", failedPlugins));
        }
    }

    @Override
    public void startPlugins() throws PluginException {
        log.info("Starting plugins...");
        List<String> failedPlugins = new ArrayList<>();
        for (Plugin plugin : pluginRegistry.getAllPlugins()) {
            try {
                startPlugin(plugin);
            } catch (Exception e) {
                String pluginId = plugin.getMetadata() != null ? plugin.getMetadata().getId() : "unknown";
                log.error("Failed to start plugin: " + pluginId, e);
                failedPlugins.add(pluginId);
            }
        }
        if (!failedPlugins.isEmpty()) {
            throw new PluginException("Failed to start plugins: " + String.join(", ", failedPlugins));
        }
    }

    @Override
    public void stopPlugins() throws PluginException {
        log.info("Stopping plugins...");

        // 逆序停止插件
        List<Plugin> reversedPlugins = new ArrayList<>(pluginRegistry.getAllPlugins());
        Collections.reverse(reversedPlugins);

        for (Plugin plugin : reversedPlugins) {
            try {
                stopPlugin(plugin);
            } catch (Exception e) {
                log.error("Failed to stop plugin: " + plugin.getMetadata().getId(), e);
                // 继续停止其他插件
            }
        }
    }

    @Override
    public void destroyPlugins() {
        log.info("Destroying plugins...");

        // 逆序销毁插件
        List<Plugin> reversedPlugins = new ArrayList<>(pluginRegistry.getAllPlugins());
        Collections.reverse(reversedPlugins);

        for (Plugin plugin : reversedPlugins) {
            try {
                destroyPlugin(plugin);
            } catch (Exception e) {
                log.error("Failed to destroy plugin: " + plugin.getMetadata().getId(), e);
                // 继续销毁其他插件
            }
        }

        // 清理类加载器
        pluginClassLoaders.values().forEach(loader -> {
            if (loader instanceof AutoCloseable) {
                try {
                    ((AutoCloseable) loader).close();
                } catch (Exception e) {
                    log.warn("Failed to close plugin class loader", e);
                }
            }
        });

        pluginClassLoaders.clear();
        pluginStates.clear();
    }

    @Override
    public Collection<Plugin> getPlugins() {
        return pluginRegistry.getAllPlugins();
    }

    @Override
    public Optional<Plugin> getPlugin(String pluginId) {
        return pluginRegistry.getPlugin(pluginId);
    }

    @Override
    public Optional<Plugin> getPlugin(String pluginId, String version) {
        return pluginRegistry.getPlugin(pluginId)
                .filter(p -> p.getMetadata().getVersion().equals(version));
    }

    @Override
    public void registerPlugin(Plugin plugin) throws PluginException {
        pluginRegistry.register(plugin);
        pluginStates.put(plugin.getMetadata().getId(), PluginState.REGISTERED);
    }

    @Override
    public boolean unregisterPlugin(String pluginId) throws PluginException {
        Plugin plugin = pluginRegistry.getPlugin(pluginId).orElse(null);
        if (plugin == null) {
            return false;
        }

        PluginState state = pluginStates.get(pluginId);
        if (state == PluginState.RUNNING) {
            stopPlugin(plugin);
        }

        if (state == PluginState.READY || state == PluginState.STOPPED) {
            destroyPlugin(plugin);
        }

        pluginStates.remove(pluginId);
        pluginClassLoaders.remove(pluginId);
        return pluginRegistry.unregister(pluginId);
    }

    @Override
    public boolean isPluginLoaded(String pluginId) {
        return pluginRegistry.hasPlugin(pluginId);
    }

    @Override
    public PluginState getPluginState(String pluginId) {
        return pluginStates.getOrDefault(pluginId, null);
    }

    @Override
    public void installPlugin(Path pluginPath, boolean force) throws PluginException {
        try {
            PluginMetadata metadata = PluginUtils.loadPluginMetadata(pluginPath.toFile());
            Path targetPath = pluginsDir.resolve(pluginPath.getFileName());

            if (Files.exists(targetPath)) {
                if (!force) {
                    throw new PluginException("Plugin " + metadata.getId() + " already exists. Use --force to overwrite.");
                }
                log.info("Plugin {} already exists, overwriting.", metadata.getId());
                // Before deleting, we need to unload the existing plugin
                if (isPluginLoaded(metadata.getId())) {
                    unregisterPlugin(metadata.getId());
                }
                Files.delete(targetPath);
            }

            Files.copy(pluginPath, targetPath);
            log.info("Plugin {} installed successfully from {}", metadata.getId(), pluginPath);

            // If the manager is running, load the new plugin immediately
            if (isRunning) {
                loadPlugin(targetPath.toFile());
            }
        } catch (Exception e) {
            throw new PluginException("Failed to install plugin from " + pluginPath, e);
        }
    }

    @Override
    public boolean uninstallPlugin(String pluginId, boolean force) throws PluginException {
        Optional<Plugin> pluginOpt = getPlugin(pluginId);
        if (pluginOpt.isEmpty()) {
            log.warn("Cannot uninstall plugin. Plugin not found: {}", pluginId);
            return false;
        }

        // 1. Unload the plugin from the manager
        unregisterPlugin(pluginId);

        // 2. Find and delete the plugin file
        File[] pluginFiles = pluginsDir.toFile().listFiles((dir, name) -> name.endsWith(".jar") || name.endsWith(".zip"));
        boolean fileDeleted = false;
        if (pluginFiles != null) {
            for (File pluginFile : pluginFiles) {
                try {
                    PluginMetadata metadata = PluginUtils.loadPluginMetadata(pluginFile);
                    if (pluginId.equals(metadata.getId())) {
                        if (pluginFile.delete()) {
                            log.info("Deleted plugin file: {}", pluginFile.getAbsolutePath());
                            fileDeleted = true;
                        } else {
                            log.error("Failed to delete plugin file: {}", pluginFile.getAbsolutePath());
                            if (!force) {
                                throw new PluginException("Failed to delete plugin file: " + pluginFile.getAbsolutePath());
                            }
                        }
                        // Assuming one pluginId corresponds to one file
                        break;
                    }
                } catch (Exception e) {
                    log.warn("Could not read metadata from {}, skipping in uninstall", pluginFile.getName());
                }
            }
        }

        if (!fileDeleted) {
            log.warn("Could not find or delete the file for pluginId: {}", pluginId);
        }

        return fileDeleted;
    }

    private void loadPlugin(File pluginFile) throws Exception {
        String pluginId = pluginFile.getName();
        log.debug("Loading plugin: {}", pluginId);

        // 1. 创建插件类加载器
        URL pluginUrl = pluginFile.toURI().toURL();
        URLClassLoader pluginClassLoader = new URLClassLoader(
            new URL[]{pluginUrl},
            getClass().getClassLoader()
        );

        // 2. 加载插件元数据
        PluginMetadata metadata = PluginUtils.loadPluginMetadata(pluginFile);
        pluginId = metadata.getId();

        // 检查插件是否已加载
        if (pluginRegistry.hasPlugin(pluginId)) {
            log.info("Plugin {} already loaded, reloading...", pluginId);
            unregisterPlugin(pluginId);
        }

        // 3. 加载插件类
        Class<?> pluginClass = pluginClassLoader.loadClass(metadata.getClassName());
        Plugin plugin = (Plugin) pluginClass.getDeclaredConstructor().newInstance();

        // 4. 注册插件
        registerPlugin(plugin);
        pluginClassLoaders.put(pluginId, pluginClassLoader);

        log.info("Loaded plugin: {} v{}", pluginId, metadata.getVersion());

        // 5. 初始化热加载的插件
        if (isRunning) {
            initPlugin(plugin);
            startPlugin(plugin);
        }
    }

    private void initPlugin(Plugin plugin) throws PluginException {
        String pluginId = plugin.getMetadata().getId();
        if (getPluginState(pluginId) != PluginState.REGISTERED) {
            log.warn("Plugin {} is not in REGISTERED state, cannot initialize.", pluginId);
            return;
        }

        try {
            // 加载插件特定配置
            Properties properties = loadPluginConfig(pluginId);
            Logger pluginLogger = LoggerFactory.getLogger("plugin." + pluginId);

            // 创建插件上下文
            PluginContext context = new DefaultPluginContext(
                this,
                pluginClassLoaders.get(pluginId),
                properties,
                pluginsDir.resolve(pluginId),
                tempDir.resolve(pluginId),
                pluginLogger
            );

            // 初始化插件
            plugin.init(context);
            pluginStates.put(pluginId, PluginState.READY);
            log.info("Plugin {} initialized.", pluginId);
        } catch (Exception e) {
            pluginStates.put(pluginId, PluginState.FAILED);
            log.error("Failed to initialize plugin: " + pluginId, e);
            throw new PluginException("Failed to initialize plugin: " + pluginId, e);
        }
    }

    /**
     * 加载全局插件配置
     */
    private void loadGlobalPluginConfig() {
        // 尝试加载不同格式的全局配置
        Properties globalConfig = new Properties();

        // 尝试加载 .properties 格式
        Path propertiesPath = configDir.resolve(PLUGIN_GLOBAL_CONFIG + ".properties");
        if (Files.exists(propertiesPath)) {
            try (InputStream in = Files.newInputStream(propertiesPath)) {
                globalConfig.load(in);
                log.debug("Loaded global plugin properties from: {}", propertiesPath);
            } catch (IOException e) {
                log.warn("Failed to load global plugin properties from: {}", propertiesPath, e);
            }
        }

        // 尝试加载 .yaml 格式
        Path yamlPath = configDir.resolve(PLUGIN_GLOBAL_CONFIG + ".yaml");
        if (Files.exists(yamlPath)) {
            try {
                Map<String, Object> yamlProps = YamlUtils.loadYaml(yamlPath.toFile());
                Map<String, String> flattenedProps = YamlUtils.flattenYamlMap(yamlProps);
                flattenedProps.forEach(globalConfig::setProperty);
                log.debug("Loaded global plugin YAML config from: {}", yamlPath);
            } catch (Exception e) {
                log.warn("Failed to load global plugin YAML config from: {}", yamlPath, e);
            }
        }

        // 尝试加载 .json 格式
        Path jsonPath = configDir.resolve(PLUGIN_GLOBAL_CONFIG + ".json");
        if (Files.exists(jsonPath)) {
            try {
                Map<String, Object> jsonProps = JsonUtils.loadJson(jsonPath.toFile());
                Map<String, String> flattenedProps = JsonUtils.flattenJsonMap(jsonProps);
                flattenedProps.forEach(globalConfig::setProperty);
                log.debug("Loaded global plugin JSON config from: {}", jsonPath);
            } catch (Exception e) {
                log.warn("Failed to load global plugin JSON config from: {}", jsonPath, e);
            }
        }

        // 存储全局配置
        pluginConfigs.put(PLUGIN_GLOBAL_CONFIG, globalConfig);
    }

    /**
     * 加载特定插件的配置
     * @param pluginId 插件ID
     * @return 插件配置
     */
    private Properties loadPluginConfig(String pluginId) {
        // 先复制全局配置
        Properties config = new Properties();
        Properties globalConfig = pluginConfigs.get(PLUGIN_GLOBAL_CONFIG);
        if (globalConfig != null) {
            globalConfig.forEach((key, value) -> config.setProperty(key.toString(), value.toString()));
        }

        // 尝试加载插件特定配置
        String configPrefix = PLUGIN_CONFIG_PREFIX + pluginId;

        // 尝试加载 .properties 格式
        Path propertiesPath = configDir.resolve(configPrefix + ".properties");
        if (Files.exists(propertiesPath)) {
            try (InputStream in = Files.newInputStream(propertiesPath)) {
                config.load(in);
                log.debug("Loaded plugin properties from: {}", propertiesPath);
            } catch (IOException e) {
                log.warn("Failed to load plugin properties from: {}", propertiesPath, e);
            }
        }

        // 尝试加载 .yaml 格式
        Path yamlPath = configDir.resolve(configPrefix + ".yaml");
        if (Files.exists(yamlPath)) {
            try {
                Map<String, Object> yamlProps = YamlUtils.loadYaml(yamlPath.toFile());
                Map<String, String> flattenedProps = YamlUtils.flattenYamlMap(yamlProps);
                flattenedProps.forEach(config::setProperty);
                log.debug("Loaded plugin YAML config from: {}", yamlPath);
            } catch (Exception e) {
                log.warn("Failed to load plugin YAML config from: {}", yamlPath, e);
            }
        }

        // 尝试加载 .json 格式
        Path jsonPath = configDir.resolve(configPrefix + ".json");
        if (Files.exists(jsonPath)) {
            try {
                Map<String, Object> jsonProps = JsonUtils.loadJson(jsonPath.toFile());
                Map<String, String> flattenedProps = JsonUtils.flattenJsonMap(jsonProps);
                flattenedProps.forEach(config::setProperty);
                log.debug("Loaded plugin JSON config from: {}", jsonPath);
            } catch (Exception e) {
                log.warn("Failed to load plugin JSON config from: {}", jsonPath, e);
            }
        }

        // 缓存并返回配置
        pluginConfigs.put(pluginId, config);
        return config;
    }

    private void startPlugin(Plugin plugin) throws PluginException {
        String pluginId = plugin.getMetadata().getId();
        PluginState currentState = pluginStates.get(pluginId);

        if (currentState != PluginState.READY && currentState != PluginState.STOPPED) {
            throw new PluginException("Cannot start plugin " + pluginId +
                " in state: " + currentState);
        }

        try {
            pluginStates.put(pluginId, PluginState.RUNNING);
            plugin.start();
            log.info("Started plugin: {}", pluginId);

        } catch (Exception e) {
            pluginStates.put(pluginId, PluginState.FAILED);
            throw new PluginException("Failed to start plugin: " + pluginId, e);
        }
    }

    private void stopPlugin(Plugin plugin) throws PluginException {
        String pluginId = plugin.getMetadata().getId();
        PluginState currentState = pluginStates.get(pluginId);

        if (currentState != PluginState.RUNNING) {
            return; // 如果插件不在运行中，则无需停止
        }

        try {
            pluginStates.put(pluginId, PluginState.STOPPED);
            plugin.stop();
            log.info("Stopped plugin: {}", pluginId);

        } catch (Exception e) {
            pluginStates.put(pluginId, PluginState.FAILED);
            log.error("Error stopping plugin: " + pluginId, e);
            throw new PluginException("Error stopping plugin: " + pluginId, e);
        }
    }

    // FileWatcher methods removed

    // File event handlers removed

    private boolean isPluginFile(Path path) {
        String fileName = path.toString().toLowerCase();
        return fileName.endsWith(".jar") || fileName.endsWith(".zip");
    }

    private void destroyPlugin(Plugin plugin) {
        String pluginId = plugin.getMetadata().getId();

        try {
            pluginStates.put(pluginId, PluginState.DESTROYING);
            plugin.destroy();
            log.info("Destroyed plugin: {}", pluginId);

        } catch (Exception e) {
            log.error("Error destroying plugin: " + pluginId, e);
        } finally {
            pluginStates.put(pluginId, PluginState.DESTROYED);
        }
    }

    // 已删除createPluginContext方法，使用DefaultPluginContext替代

    @Override
    public void enableHotReload(Path directory, long pollInterval) throws PluginException {
        if (hotReloadEnabled) {
            throw new PluginException("Hot reload already enabled");
        }
        
        try {
            this.pollInterval = pollInterval;
            this.watchService = FileSystems.getDefault().newWatchService();
            
            directory.register(watchService, 
                StandardWatchEventKinds.ENTRY_CREATE,
                StandardWatchEventKinds.ENTRY_DELETE,
                StandardWatchEventKinds.ENTRY_MODIFY);
            
            this.watchThread = new Thread(this::watchPluginsDir, "PluginHotReload-Watcher");
            watchThread.setDaemon(true);
            watchThread.start();
            
            hotReloadEnabled = true;
            log.info("Enabled plugin hot reload for directory: {}", directory);
        } catch (IOException e) {
            throw new PluginException("Failed to enable hot reload", e);
        }
    }

    @Override
    public void disableHotReload() {
        if (!hotReloadEnabled) return;
        
        hotReloadEnabled = false;
        if (watchThread != null) {
            watchThread.interrupt();
        }
        try {
            if (watchService != null) {
                watchService.close();
            }
        } catch (IOException e) {
            log.warn("Error closing watch service", e);
        }
        log.info("Disabled plugin hot reload");
    }

    @Override
    public boolean isHotReloadEnabled() {
        return hotReloadEnabled;
    }

    private void watchPluginsDir() {
        try {
            while (hotReloadEnabled && !Thread.currentThread().isInterrupted()) {
                WatchKey key = watchService.poll(pollInterval, TimeUnit.MILLISECONDS);
                if (key == null) continue;

                for (WatchEvent<?> event : key.pollEvents()) {
                    Path changedFile = (Path) event.context();
                    Path fullPath = pluginsDir.resolve(changedFile);
                    String fileName = changedFile.toString();

                    if (fileName.endsWith(".jar")) {
                        handlePluginChange(event.kind(), fullPath);
                    }
                }
                key.reset();
            }
        } catch (InterruptedException e) {
            log.info("Plugin hot reload watcher interrupted");
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("Error in plugin hot reload watcher", e);
        }
    }

    private void handlePluginChange(WatchEvent.Kind<?> kind, Path pluginPath) {
        String pluginId = PluginUtils.getPluginIdFromFile(pluginPath);
        
        // 检查过滤条件
        if (shouldFilterPlugin(pluginId)) {
            log.debug("Plugin {} filtered by blacklist/whitelist rules", pluginId);
            return;
        }
        
        try {
            if (kind == StandardWatchEventKinds.ENTRY_CREATE) {
                log.info("Detected new plugin: {}", pluginId);
                notifyPluginEvent(new PluginEvent(PluginEvent.Type.CREATED, pluginId));
                installPlugin(pluginPath, true);
            } 
            else if (kind == StandardWatchEventKinds.ENTRY_DELETE) {
                log.info("Detected plugin removal: {}", pluginId);
                notifyPluginEvent(new PluginEvent(PluginEvent.Type.DELETED, pluginId));
                uninstallPlugin(pluginId, true);
            }
            else if (kind == StandardWatchEventKinds.ENTRY_MODIFY) {
                log.info("Detected plugin modification: {}", pluginId);
                notifyPluginEvent(new PluginEvent(PluginEvent.Type.MODIFIED, pluginId));
                uninstallPlugin(pluginId, true);
                installPlugin(pluginPath, true);
            }
        } catch (PluginException e) {
            log.error("Failed to handle plugin change for {}", pluginPath, e);
            notifyPluginEvent(new PluginEvent(PluginEvent.Type.ERROR, pluginId, e));
        }
    }

    private boolean shouldFilterPlugin(String pluginId) {
        if (useWhitelist) {
            return !whitelist.contains(pluginId);
        }
        return blacklist.contains(pluginId);
    }

    public void addEventListener(Consumer<PluginEvent> listener) {
        eventListeners.add(listener);
    }

    public void removeEventListener(Consumer<PluginEvent> listener) {
        eventListeners.remove(listener);
    }

    private void notifyPluginEvent(PluginEvent event) {
        eventListeners.forEach(listener -> {
            try {
                listener.accept(event);
            } catch (Exception e) {
                log.error("Error notifying plugin event", e);
            }
        });
    }
}
