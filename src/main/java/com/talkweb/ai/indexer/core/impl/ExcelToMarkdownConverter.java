

package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.DocumentConverter;
import com.talkweb.ai.indexer.core.ConversionResult;
import com.talkweb.ai.indexer.core.ConversionException;
import com.talkweb.ai.indexer.core.Plugin;
import com.talkweb.ai.indexer.core.PluginMetadata;
import com.talkweb.ai.indexer.core.PluginState;
import com.talkweb.ai.indexer.core.PluginContext;
import com.talkweb.ai.indexer.core.PluginException;
import org.apache.poi.ss.usermodel.*;
import java.io.File;
import java.nio.file.Path;

public class ExcelToMarkdownConverter implements DocumentConverter, Plugin {

    private final PluginMetadata metadata;
    private boolean initialized = true;

    public ExcelToMarkdownConverter(PluginMetadata metadata) {
        this.metadata = metadata;
    }

    @Override
    public void destroy() {
        // 清理资源
        this.initialized = false;
    }
    
    @Override
    public void stop() throws PluginException {
        this.initialized = false;
    }

    @Override
    public PluginMetadata getMetadata() {
        return metadata;
    }

    @Override
    public PluginState getState() {
        return initialized ? PluginState.RUNNING : PluginState.STOPPED;
    }

    @Override
    public void init(PluginContext context) throws PluginException {
        this.initialized = true;
    }

    @Override
    public void start() throws PluginException {
        this.initialized = true;
    }

    @Override
    public ConversionResult convert(File inputFile) throws ConversionException {
        Path outputPath = Path.of(inputFile.getParent(), inputFile.getName() + ".md");
        try (Workbook workbook = WorkbookFactory.create(inputFile)) {
            StringBuilder markdown = new StringBuilder();
            FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();

            // 处理每个sheet
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                markdown.append("## ").append(sheet.getSheetName()).append("\n\n");

                // 转换表格数据
                markdown.append(convertSheetToMarkdown(sheet, evaluator));
                markdown.append("\n\n");
            }

            return new ConversionResult(
                ConversionResult.Status.SUCCESS,
                inputFile.getPath(),
                outputPath.toString(),
                markdown.toString()
            );
        } catch (Exception e) {
            throw new ConversionException("Excel转换失败: " + e.getMessage(), e);
        }
    }

    private String convertSheetToMarkdown(Sheet sheet, FormulaEvaluator evaluator) {
        StringBuilder table = new StringBuilder();
        DataFormatter dataFormatter = new DataFormatter();
        for (Row row : sheet) {
            for (Cell cell : row) {
                table.append("| ").append(dataFormatter.formatCellValue(cell, evaluator)).append(" ");
            }
            table.append("|\n");

            // 添加表头分隔线
            if (row.getRowNum() == 0) {
                for (int i = 0; i < row.getLastCellNum(); i++) {
                    table.append("| --- ");
                }
                table.append("|\n");
            }
        }
        return table.toString();
    }

    @Override
    public boolean supportsExtension(String fileExtension) {
        if (fileExtension == null) {
            return false;
        }
        return fileExtension.equalsIgnoreCase("xlsx") ||
               fileExtension.equalsIgnoreCase("xls");
    }


}

