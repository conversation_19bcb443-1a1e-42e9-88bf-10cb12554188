package com.talkweb.ai.indexer.examples;

import com.talkweb.ai.indexer.core.ConversionResult;
import com.talkweb.ai.indexer.core.PluginMetadata;
import com.talkweb.ai.indexer.core.impl.ExcelToMarkdownConverter;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.Map;

/**
 * Example demonstrating the enhanced Excel to Markdown converter capabilities
 */
public class EnhancedExcelConverterExample {

    public static void main(String[] args) {
        try {
            // Create sample Excel files to demonstrate different features
            File basicFile = createBasicExcelFile();
            File mergedFile = createMergedCellsExcelFile();
            File formulaFile = createFormulaExcelFile();
            File complexFile = createComplexExcelFile();

            // Create converter instance
            PluginMetadata metadata = createMetadata();
            ExcelToMarkdownConverter converter = new ExcelToMarkdownConverter(metadata);

            System.out.println("=== Enhanced Excel to Markdown Converter Demo ===\n");

            // Test basic conversion
            System.out.println("1. Basic Excel Conversion:");
            testConversion(converter, basicFile);

            // Test merged cells handling
            System.out.println("\n2. Merged Cells Handling:");
            testConversion(converter, mergedFile);

            // Test formula handling
            System.out.println("\n3. Formula Handling:");
            testConversion(converter, formulaFile);

            // Test complex document
            System.out.println("\n4. Complex Document:");
            testConversion(converter, complexFile);

            // Show cache statistics
            System.out.println("\n5. Cache Statistics:");
            showCacheStatistics();

            // Clean up
            cleanupFiles(basicFile, mergedFile, formulaFile, complexFile);

        } catch (Exception e) {
            System.err.println("Error in example: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static PluginMetadata createMetadata() {
        return new PluginMetadata() {
            @Override
            public String getId() { return "enhanced-excel-converter"; }
            
            @Override
            public String getVersion() { return "2.0"; }
            
            @Override
            public String getDescription() { return "Enhanced Excel to Markdown Converter"; }
        };
    }

    private static void testConversion(ExcelToMarkdownConverter converter, File file) {
        try {
            System.out.println("Converting: " + file.getName());
            ConversionResult result = converter.convert(file);
            
            if (result.getStatus() == ConversionResult.Status.SUCCESS) {
                System.out.println("✓ Conversion successful");
                System.out.println("Content preview (first 200 chars):");
                String content = result.getContent();
                String preview = content.length() > 200 ? content.substring(0, 200) + "..." : content;
                System.out.println(preview);
            } else {
                System.out.println("✗ Conversion failed");
            }
        } catch (Exception e) {
            System.out.println("✗ Error: " + e.getMessage());
        }
    }

    private static void showCacheStatistics() {
        Map<String, Integer> stats = ExcelToMarkdownConverter.getCacheStatistics();
        System.out.println("Cache Statistics:");
        for (Map.Entry<String, Integer> entry : stats.entrySet()) {
            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
        }
    }

    private static File createBasicExcelFile() throws IOException {
        File file = new File("basic_example.xlsx");
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Employee Data");
            
            // Header row
            Row header = sheet.createRow(0);
            header.createCell(0).setCellValue("Name");
            header.createCell(1).setCellValue("Age");
            header.createCell(2).setCellValue("Department");
            header.createCell(3).setCellValue("Salary");
            
            // Data rows
            String[][] data = {
                {"John Doe", "30", "Engineering", "75000"},
                {"Jane Smith", "28", "Marketing", "65000"},
                {"Bob Johnson", "35", "Sales", "70000"}
            };
            
            for (int i = 0; i < data.length; i++) {
                Row row = sheet.createRow(i + 1);
                for (int j = 0; j < data[i].length; j++) {
                    if (j == 1 || j == 3) { // Age and Salary as numbers
                        row.createCell(j).setCellValue(Double.parseDouble(data[i][j]));
                    } else {
                        row.createCell(j).setCellValue(data[i][j]);
                    }
                }
            }
            
            try (FileOutputStream fos = new FileOutputStream(file)) {
                workbook.write(fos);
            }
        }
        return file;
    }

    private static File createMergedCellsExcelFile() throws IOException {
        File file = new File("merged_example.xlsx");
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Quarterly Report");
            
            // Merged header
            Row titleRow = sheet.createRow(0);
            titleRow.createCell(0).setCellValue("Quarterly Sales Report - Q1 2024");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 3));
            
            // Sub headers
            Row subHeader = sheet.createRow(2);
            subHeader.createCell(0).setCellValue("Product");
            subHeader.createCell(1).setCellValue("Q1 Sales");
            subHeader.createCell(2).setCellValue("Target");
            subHeader.createCell(3).setCellValue("Achievement %");
            
            // Data
            Row data1 = sheet.createRow(3);
            data1.createCell(0).setCellValue("Product A");
            data1.createCell(1).setCellValue(150000);
            data1.createCell(2).setCellValue(120000);
            data1.createCell(3).setCellValue(125.0);
            
            try (FileOutputStream fos = new FileOutputStream(file)) {
                workbook.write(fos);
            }
        }
        return file;
    }

    private static File createFormulaExcelFile() throws IOException {
        File file = new File("formula_example.xlsx");
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Calculations");
            
            // Headers
            Row header = sheet.createRow(0);
            header.createCell(0).setCellValue("Item");
            header.createCell(1).setCellValue("Quantity");
            header.createCell(2).setCellValue("Price");
            header.createCell(3).setCellValue("Total");
            
            // Data with formulas
            Row row1 = sheet.createRow(1);
            row1.createCell(0).setCellValue("Widget A");
            row1.createCell(1).setCellValue(10);
            row1.createCell(2).setCellValue(25.50);
            row1.createCell(3).setCellFormula("B2*C2");
            
            Row row2 = sheet.createRow(2);
            row2.createCell(0).setCellValue("Widget B");
            row2.createCell(1).setCellValue(5);
            row2.createCell(2).setCellValue(45.00);
            row2.createCell(3).setCellFormula("B3*C3");
            
            // Sum formula
            Row totalRow = sheet.createRow(3);
            totalRow.createCell(0).setCellValue("Total");
            totalRow.createCell(3).setCellFormula("SUM(D2:D3)");
            
            try (FileOutputStream fos = new FileOutputStream(file)) {
                workbook.write(fos);
            }
        }
        return file;
    }

    private static File createComplexExcelFile() throws IOException {
        File file = new File("complex_example.xlsx");
        try (Workbook workbook = new XSSFWorkbook()) {
            // Sheet 1: Sales Data
            Sheet salesSheet = workbook.createSheet("Sales Data");
            createSalesData(salesSheet);
            
            // Sheet 2: Summary
            Sheet summarySheet = workbook.createSheet("Summary");
            createSummaryData(summarySheet);
            
            try (FileOutputStream fos = new FileOutputStream(file)) {
                workbook.write(fos);
            }
        }
        return file;
    }

    private static void createSalesData(Sheet sheet) {
        // Headers
        Row header = sheet.createRow(0);
        String[] headers = {"Date", "Product", "Salesperson", "Quantity", "Unit Price", "Total"};
        for (int i = 0; i < headers.length; i++) {
            header.createCell(i).setCellValue(headers[i]);
        }
        
        // Sample data
        Object[][] data = {
            {new Date(), "Product A", "John", 10, 25.50, "=D2*E2"},
            {new Date(), "Product B", "Jane", 5, 45.00, "=D3*E3"},
            {new Date(), "Product A", "Bob", 8, 25.50, "=D4*E4"}
        };
        
        for (int i = 0; i < data.length; i++) {
            Row row = sheet.createRow(i + 1);
            for (int j = 0; j < data[i].length; j++) {
                Cell cell = row.createCell(j);
                Object value = data[i][j];
                if (value instanceof String && ((String) value).startsWith("=")) {
                    cell.setCellFormula(((String) value).substring(1));
                } else if (value instanceof Number) {
                    cell.setCellValue(((Number) value).doubleValue());
                } else if (value instanceof Date) {
                    cell.setCellValue((Date) value);
                } else {
                    cell.setCellValue(value.toString());
                }
            }
        }
    }

    private static void createSummaryData(Sheet sheet) {
        Row titleRow = sheet.createRow(0);
        titleRow.createCell(0).setCellValue("Sales Summary");
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 2));
        
        Row row1 = sheet.createRow(2);
        row1.createCell(0).setCellValue("Total Sales:");
        row1.createCell(1).setCellValue("$500.00");
        
        Row row2 = sheet.createRow(3);
        row2.createCell(0).setCellValue("Number of Transactions:");
        row2.createCell(1).setCellValue(3);
    }

    private static void cleanupFiles(File... files) {
        for (File file : files) {
            if (file.exists()) {
                file.delete();
            }
        }
    }
}
