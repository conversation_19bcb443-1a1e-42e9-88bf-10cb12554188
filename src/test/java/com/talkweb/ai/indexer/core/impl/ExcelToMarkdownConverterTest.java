package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.ConversionException;
import com.talkweb.ai.indexer.core.ConversionResult;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

class ExcelToMarkdownConverterTest {

    private ExcelToMarkdownConverter converter;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        converter = new ExcelToMarkdownConverter(null); // Metadata not used in conversion
    }

    @Test
    void testSupportsExtension() {
        assertTrue(converter.supportsExtension("xlsx"));
        assertTrue(converter.supportsExtension("xls"));
        assertFalse(converter.supportsExtension("csv"));
        assertFalse(converter.supportsExtension(null));
    }

    @Test
    void testConvertExcelToMarkdown() throws IOException, ConversionException {
        File excelFile = createTestExcelFile("test.xlsx");

        ConversionResult result = converter.convert(excelFile);

        assertNotNull(result);
        assertEquals(ConversionResult.Status.SUCCESS, result.getStatus());
        assertNotNull(result.getMarkdownContent());

        String expectedMarkdown = "## Sheet1\n\n| Header 1 | Header 2 |\n| --- | --- |\n| R1C1 | R1C2 |\n| R2C1 | R2C2 |\n\n";
        // Normalize line endings for comparison
        String actualMarkdown = result.getMarkdownContent().replaceAll("\r\n", "\n");
        assertTrue(actualMarkdown.contains("| Header 1 | Header 2 |"));
        assertTrue(actualMarkdown.contains("| R1C1 | R1C2 |"));
    }

    private File createTestExcelFile(String fileName) throws IOException {
        Path filePath = tempDir.resolve(fileName);
        try (Workbook workbook = new XSSFWorkbook();
             FileOutputStream fos = new FileOutputStream(filePath.toFile())) {
            Sheet sheet = workbook.createSheet("Sheet1");

            // Header
            Row headerRow = sheet.createRow(0);
            Cell header1 = headerRow.createCell(0);
            header1.setCellValue("Header 1");
            Cell header2 = headerRow.createCell(1);
            header2.setCellValue("Header 2");

            // Data Row 1
            Row row1 = sheet.createRow(1);
            row1.createCell(0).setCellValue("R1C1");
            row1.createCell(1).setCellValue("R1C2");

            // Data Row 2
            Row row2 = sheet.createRow(2);
            row2.createCell(0).setCellValue("R2C1");
            row2.createCell(1).setCellValue("R2C2");

            workbook.write(fos);
        }
        return filePath.toFile();
    }
}
